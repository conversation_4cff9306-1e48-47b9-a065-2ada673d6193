# Matrix私有化服务栈自动化部署与管理系统实施计划

## 实施计划概述

基于Element ESS Helm项目的深入分析，将Kubernetes架构转换为Podman容器化部署，实现从零到一的自动化部署和完整的运维管理功能。

## 任务列表

- [ ] 1. 环境验证与净化系统实现
  - 实现SSH连接到10.0.0.251的自动验证
  - 开发环境指纹识别和验证机制
  - 实现彻底的系统净化协议(Scorched Earth Protocol)
  - 确保服务器恢复到Debian 12初始状态
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9_

- [ ] 2. Element ESS配置模板转换系统
  - 分析并转换Synapse配置模板(synapse-04-homeserver-overrides.yaml.tpl)
  - 转换MAS配置模板(config.yaml.tpl)为Podman环境
  - 转换Element Web配置模板(config.json.tpl)
  - 实现HAProxy配置模板转换(partial-haproxy.cfg.tpl)
  - 创建配置模板渲染引擎，支持变量替换
  - _需求: 12.1, 12.2, 12.3, 12.4, 12.5_

- [ ] 3. 密钥管理和初始化系统
  - 实现initSecrets功能，自动生成所有服务密钥
  - 生成Synapse签名密钥、macaroon密钥、注册共享密钥
  - 生成MAS加密密钥、RSA/ECDSA私钥、OIDC客户端密钥
  - 生成PostgreSQL管理员和用户密码
  - 生成Matrix RTC LiveKit密钥
  - 实现密钥文件安全存储(权限600)
  - _需求: 3.5, 3.6_

- [ ] 4. PostgreSQL数据库容器部署
  - 部署PostgreSQL 15容器，基于ESS配置
  - 创建synapse和matrixauthenticationservice数据库
  - 配置数据库用户和权限(synapse_user, matrixauthenticationservice_user)
  - 实现数据持久化(Podman Volume)
  - 配置连接参数(sslmode, keepalives等)
  - 集成Postgres Exporter监控
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 5. Synapse Homeserver容器部署
  - 部署Synapse主容器，使用matrixdotorg/synapse:latest
  - 配置端口映射(8008客户端API, 8448联邦API, 9093复制)
  - 实现配置文件挂载和模板渲染
  - 配置媒体存储持久化
  - 实现与PostgreSQL的数据库连接
  - 配置与MAS的OIDC集成(MSC3861)
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [ ] 6. Matrix Authentication Service容器部署
  - 部署MAS容器，使用ghcr.io/element-hq/matrix-authentication-service:0.19.0
  - 配置双端口服务(8080 Web, 8081 Internal)
  - 实现与PostgreSQL的数据库连接
  - 配置RSA/ECDSA私钥管理
  - 实现与Synapse的集成配置
  - 配置OIDC客户端(0000000000000000000SYNAPSE)
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [ ] 7. Element Web客户端容器部署
  - 部署Element Web容器，使用ghcr.io/element-hq/element-web:v1.11.106
  - 实现config.json配置生成，基于ESS模板
  - 配置MAS集成(embedded_pages, sso_redirect_options)
  - 配置Matrix RTC集成(feature_group_calls, element_call)
  - 实现自定义配置支持(additional配置)
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 8. Matrix RTC服务容器部署
  - 部署LiveKit JWT服务，使用ghcr.io/element-hq/lk-jwt-service:0.2.3
  - 配置LiveKit密钥管理(keys.yaml或key/secret)
  - 实现与外部LiveKit服务器的集成
  - 配置Prometheus监控集成
  - 实现视频通话JWT认证功能
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 9. Redis Pub/Sub容器部署(Worker支持)
  - 部署Redis容器，支持Synapse Worker通信
  - 配置redis.conf，基于ESS配置
  - 实现与Synapse的集成配置
  - 配置内存限制和持久化
  - 为多Worker部署做准备
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 10. HAProxy负载均衡器容器部署
  - 部署HAProxy容器，基于ESS配置
  - 实现Matrix API流量分发配置
  - 配置.well-known文件服务
  - 实现健康检查和故障转移
  - 配置与Synapse和MAS的路由规则
  - 支持Worker负载均衡(为未来扩展)
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 11. Podman网络和服务发现配置
  - 创建matrix-network Podman网络
  - 实现容器间DNS解析
  - 配置内部服务发现机制
  - 实现端口映射和网络隔离
  - 配置防火墙端口开放策略
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 12. Systemd Quadlet集成
  - 为所有容器创建Systemd Quadlet配置文件
  - 实现服务依赖关系管理
  - 配置自动启动和重启策略
  - 实现服务状态监控
  - 支持rootless Podman运行
  - _需求: 3.4, 3.5_

- [ ] 13. 证书管理系统实现
  - 实现acme.sh集成，使用默认路径
  - 配置DNS-01挑战方式(支持自定义端口)
  - 实现软连接策略，避免重复申请
  - 支持通配符证书(*.example.com)
  - 实现自动续期机制
  - 配置证书权限和安全存储
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 14. 动态IP和DNS更新服务
  - 实现Cloudflare API集成
  - 实现RouterOS API集成
  - 开发动态IP检测和更新机制
  - 配置DNS记录自动更新
  - 实现错误处理和重试机制
  - _需求: 3.3, 3.4_

- [ ] 15. Nginx反向代理配置
  - 配置内部服务器Nginx，处理所有子域名
  - 实现SSL终止和证书管理
  - 配置自定义端口映射(8080, 8443, 8448等)
  - 实现到各服务的反向代理规则
  - 配置安全头和访问控制
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 16. 外部服务器部署脚本(deploy_external.sh)
  - 实现.well-known委派配置
  - 配置主域名证书申请(example.com)
  - 实现简化的Nginx配置
  - 配置指向内部服务器的委派
  - 实现部署验证和测试
  - _需求: 4.1, 4.2, 4.3_

- [ ] 17. 内部服务器部署脚本(deploy_internal.sh)
  - 实现完整的服务栈自动化部署
  - 集成所有组件的部署逻辑
  - 实现依赖安装和环境配置
  - 配置信息收集和验证
  - 实现部署后验证和测试
  - 生成初始管理员账户和访问令牌
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [ ] 18. 统一部署入口脚本(setup.sh)
  - 实现部署目标选择菜单
  - 集成环境验证和净化
  - 调用相应的部署脚本
  - 实现错误处理和日志记录
  - 提供部署进度显示
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 19. 管理控制台核心功能(matrix_ctl.sh)
  - 实现交互式菜单界面
  - 显示当前配置状态(注册模式、联邦状态)
  - 实现操作确认和安全提示
  - 集成Synapse Admin API调用
  - 实现配置文件修改和服务重启
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [ ] 20. 用户注册管理功能
  - 实现注册模式切换(开放/邀请制)
  - 修改homeserver.yaml中的enable_registration配置
  - 实现配置生效的服务重启
  - 提供当前状态显示
  - 实现操作日志记录
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 21. 联邦功能管理
  - 实现联邦功能开关切换
  - 修改homeserver.yaml中的federation配置
  - 实现严重警告提示机制
  - 配置监听器的启用/禁用
  - 实现配置生效的服务重启
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 22. 用户管理功能实现
  - 实现用户管理子菜单
  - 集成Synapse Admin API用户操作
  - 实现用户创建功能(/_synapse/admin/v2/users)
  - 实现管理员权限设置/取消
  - 实现用户删除功能，包含二次确认
  - 实现用户列表显示和格式化
  - 实现密码重置功能
  - _需求: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6_

- [ ] 23. 服务管理功能
  - 实现所有matrix-stack服务的重启功能
  - 显示重启进度和状态
  - 实现服务状态验证
  - 提供单个服务重启选项
  - 实现服务依赖关系处理
  - _需求: 9.1, 9.2, 9.3_

- [ ] 24. 系统测试和验证框架
  - 实现单元验证测试套件
  - 开发动态IP模拟测试
  - 实现用户流程端到端测试
  - 开发管理脚本功能测试
  - 实现联邦功能测试
  - 创建自动化测试脚本
  - _需求: 10.1, 10.2, 10.3, 10.4, 10.5_

- [ ] 25. 错误处理和日志系统
  - 实现统一的错误处理机制
  - 开发详细的日志记录系统
  - 实现错误恢复和重试逻辑
  - 创建故障诊断工具
  - 实现操作审计日志
  - _需求: 所有需求的错误处理_

- [ ] 26. 配置文件模板和文档
  - 创建所有配置文件的详细注释版本
  - 编写参数说明和效果示例
  - 创建配置模板库
  - 实现配置验证工具
  - 编写配置最佳实践文档
  - _需求: 13.4, 13.5_

- [ ] 27. 部署包构建和打包
  - 创建完整的部署包(matrix-stack-deploy.tar.gz)
  - 包含所有脚本和配置模板
  - 实现版本管理和更新机制
  - 创建安装和卸载脚本
  - 实现部署包验证
  - _需求: 13.1_

- [ ] 28. 用户文档编写
  - 编写"傻瓜式"部署指南(MANUAL_DEPLOYMENT_GUIDE.md)
  - 创建日常管理和维护章节
  - 编写故障排查FAQ
  - 提供可复制粘贴的命令示例
  - 创建架构说明和最佳实践
  - _需求: 13.2, 13.3, 13.5_

- [ ] 29. 性能优化和监控集成
  - 实现Prometheus监控集成
  - 配置服务性能监控
  - 实现资源使用优化
  - 创建性能调优指南
  - 实现告警和通知机制
  - _需求: 性能和监控相关_

- [ ] 30. 最终集成测试和验证
  - 执行完整的端到端部署测试
  - 验证所有功能模块的集成
  - 进行压力测试和稳定性测试
  - 验证管理功能的完整性
  - 执行安全测试和漏洞扫描
  - 完成最终的文档验证
  - _需求: 所有需求的最终验证_