# Matrix私有化服务栈自动化部署与管理系统需求文档

## 介绍

本项目旨在开发一个全栈Matrix私有化服务栈的自动化部署、封装与持续管理系统。该系统将提供从零到一的自动化部署能力，以及部署后的日常管理功能，支持内外网架构，严格基于Podman容器化技术（不使用Docker Compose），并完整参考Element ESS Helm项目(https://github.com/element-hq/ess-helm)中的所有配置和架构设计，确保高安全性和易用性。

## 需求

### 需求 1 - 强制性环境验证与净化 (不可跳过)

**用户故事:** 作为DevOps工程师，我希望系统在执行任何文件创建或系统修改之前，必须首先验证并确认当前操作环境为指定的开发/测试环境，这是不可协商的第一道安全门。

#### 验收标准

1. 当系统启动时，系统应当首先通过SSH连接到目标主机(**********)，使用用户名jw和密码test123
2. 当SSH连接建立后，系统应当立即执行命令确认主机身份和环境指纹
3. 当环境验证通过后，系统应当执行彻底的系统净化协议(Scorched Earth Protocol)
4. 当净化过程执行时，系统应当停止并删除所有正在运行的podman和docker容器
5. 当净化过程执行时，系统应当删除所有容器镜像
6. 当净化过程执行时，系统应当卸载所有非系统默认的软件包(如nginx、python3-pip、podman等)
7. 当净化过程执行时，系统应当清理系统范围内的配置文件、残留数据和日志
8. 当净化过程执行时，系统不得修改核心系统组件、网络接口配置或SSH服务本身
9. 当净化完成后，系统应当确保服务器状态等同于全新的Debian 12系统

### 需求 2 - 统一部署入口

**用户故事:** 作为系统管理员，我希望有一个统一的部署入口脚本，能够根据不同的部署目标自动选择相应的部署策略。

#### 验收标准

1. 当用户运行setup.sh时，系统应当提供清晰的部署目标选择菜单
2. 当用户选择内部服务器时，系统应当调用内部服务器部署脚本
3. 当用户选择外部服务器时，系统应当调用外部服务器部署脚本
4. 当脚本启动时，系统应当自动下载所需的管理脚本

### 需求 3 - 内部服务器自动化部署

**用户故事:** 作为系统管理员，我希望能够一键部署完整的Matrix服务栈到内部服务器，包括所有必要的组件和配置。

#### 验收标准

1. 当执行内部部署时，系统应当自动安装所有依赖组件
2. 当部署过程中，系统应当收集必要的配置信息
3. 当部署完成时，系统应当配置动态IP服务和证书申请
4. 当证书申请时，系统应当使用acme.sh的默认路径创建证书并使用软连接，避免重复申请证书问题
5. 当部署完成时，系统应当使用Podman Quadlet进行服务部署
6. 当部署结束时，系统应当自动生成初始管理员账户和访问令牌
7. 当服务栈部署完成时，系统应当包含以下组件：Synapse、Element-Web、MAS、Element Call、LiveKit、JWT服务、Coturn、PostgreSQL、Nginx
8. 当容器化部署时，系统应当严格使用Podman而非Docker Compose
9. 当架构设计时，系统应当完整参考Element ESS Helm项目的所有配置文件和架构模式

### 需求 4 - 外部服务器部署

**用户故事:** 作为系统管理员，我希望能够在外部服务器上配置.well-known文件和证书，以支持Matrix联邦功能。

#### 验收标准

1. 当执行外部部署时，系统应当配置.well-known委派文件
2. 当外部部署执行时，系统应当使用acme.sh申请和配置SSL证书，使用默认路径并创建软连接
3. 当证书管理时，系统应当避免重复申请证书导致的速率限制问题
4. 当配置完成时，系统应当验证.well-known文件的可访问性

### 需求 5 - 证书管理与自动化

**用户故事:** 作为系统管理员，我希望系统能够智能管理SSL证书，使用acme.sh的默认路径并通过软连接避免重复申请问题。

#### 验收标准

1. 当申请证书时，系统应当使用acme.sh的默认安装路径和配置
2. 当证书创建后，系统应当在服务配置中使用软连接指向acme.sh默认路径
3. 当证书更新时，系统应当自动通过软连接生效，无需重新配置服务
4. 当检测到现有证书时，系统应当避免重复申请以防止速率限制
5. 当证书即将过期时，系统应当自动续期并通过软连接无缝更新

### 需求 6 - 部署后管理控制台

**用户故事:** 作为系统管理员，我希望有一个交互式的管理控制台，能够方便地进行日常维护操作。

#### 验收标准

1. 当运行管理脚本时，系统应当显示清晰的菜单界面
2. 当用户选择功能时，系统应当提供操作确认提示
3. 当管理脚本启动时，系统应当显示当前配置状态
4. 当执行配置修改时，系统应当自动重启相关服务

### 需求 7 - 用户注册管理

**用户故事:** 作为系统管理员，我希望能够灵活控制用户注册策略，在开放注册和邀请制之间切换。

#### 验收标准

1. 当选择用户注册管理时，系统应当显示当前注册状态
2. 当切换注册模式时，系统应当修改相应的配置文件
3. 当配置修改后，系统应当自动重启Synapse服务
4. 当设置为邀请制时，系统应当禁用公开注册功能
5. 当设置为开放注册时，系统应当启用公开注册功能

### 需求 8 - 联邦功能管理

**用户故事:** 作为系统管理员，我希望能够控制Matrix服务器的联邦功能，以决定是否与其他Matrix服务器通信。

#### 验收标准

1. 当选择联邦功能管理时，系统应当显示当前联邦状态
2. 当关闭联邦功能前，系统应当显示严重警告信息
3. 当切换联邦配置时，系统应当修改homeserver.yaml配置
4. 当配置修改后，系统应当自动重启相关服务
5. 当联邦功能关闭时，系统应当无法与外部Matrix服务器通信

### 需求 9 - 用户管理功能

**用户故事:** 作为系统管理员，我希望能够通过管理界面创建、修改和删除用户账户。

#### 验收标准

1. 当选择用户管理时，系统应当进入用户管理子菜单
2. 当创建新用户时，系统应当通过Admin API创建用户账户
3. 当设置管理员权限时，系统应当修改用户的admin状态
4. 当删除用户时，系统应当要求二次确认并擦除用户数据
5. 当列出用户时，系统应当格式化显示所有用户信息
6. 当重置密码时，系统应当通过API更新用户密码

### 需求 10 - 服务管理

**用户故事:** 作为系统管理员，我希望能够方便地重启所有Matrix相关服务。

#### 验收标准

1. 当选择重启服务时，系统应当重启所有matrix-stack相关的systemd服务
2. 当重启过程中，系统应当显示重启进度和状态
3. 当重启完成后，系统应当验证所有服务的运行状态

### 需求 11 - 系统测试与验证

**用户故事:** 作为质量保证工程师，我希望系统能够进行全面的功能测试，确保部署的可靠性。

#### 验收标准

1. 当部署完成后，系统应当执行单元验证测试
2. 当测试执行时，系统应当模拟动态IP变更场景
3. 当测试执行时，系统应当验证用户注册和登录流程
4. 当测试执行时，系统应当验证管理脚本的所有功能
5. 当测试执行时，系统应当验证联邦功能的开关效果

### 需求 12 - Element ESS Helm项目完整参考

**用户故事:** 作为DevOps架构师，我希望系统完整参考Element ESS Helm项目的所有配置和架构设计，确保与官方最佳实践保持一致。

#### 验收标准

1. 当设计架构时，系统应当分析Element ESS Helm项目的每一个配置文件
2. 当实现组件时，系统应当参考Helm charts中的所有服务配置
3. 当配置网络时，系统应当遵循ESS项目的网络拓扑设计
4. 当设置安全策略时，系统应当采用ESS项目的安全配置模式
5. 当部署服务时，系统应当将Helm配置转换为等效的Podman配置

### 需求 13 - 部署包与文档

**用户故事:** 作为最终用户，我希望获得完整的部署包和详细的使用文档，能够轻松完成部署和管理。

#### 验收标准

1. 当打包完成时，系统应当生成包含所有脚本的tar.gz文件
2. 当文档编写时，系统应当提供傻瓜式的部署指南
3. 当文档编写时，系统应当包含详细的日常管理章节
4. 当配置文件生成时，系统应当包含详细的参数说明注释
5. 当指南编写时，系统应当提供可直接复制粘贴的命令示例